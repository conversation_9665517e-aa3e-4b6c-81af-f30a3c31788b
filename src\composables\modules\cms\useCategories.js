/**
 * Category Management Composable
 * Handles all category management business logic
 */

import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { categoriesApi } from '@/utils/apis/index.js'

export function useCategories() {
  // State
  const loading = ref(false)
  const categories = ref([])
  const currentCategory = ref(null)
  const pagination = ref({
    current_page: 1,
    per_page: 20,
    total: 0,
    from: 0,
    to: 0,
  })
  const searchParams = reactive({
    search: '',
    status: '', // Add status field here
    page: 1,
    limit: 20,
  })

  // Category CRUD operations
  const fetchCategories = async (params = {}) => {
    try {
      loading.value = true
      const mergedParams = { ...searchParams, ...params }
      
      const response = await categoriesApi.getCategories(mergedParams)


      if (response?.data?.success) {
        categories.value = response?.data?.data?.data || []
        const paginationData = response.data.data.pagination || { total: 0 }

        // Update pagination with API response
        pagination.value = {
          current_page: paginationData.current_page || 1,
          per_page: paginationData.per_page || 15,
          total: paginationData.total || 0,
          last_page: paginationData.last_page || 1,
          from: paginationData.from || 0,
          to: paginationData.to || 0,
        }

        return response.data
      } else {
        throw new Error(response?.data?.message || 'Không thể tải danh sách danh mục')
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Không thể tải danh sách danh mục'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  const createCategory = async (categoryData) => {
    try {
      loading.value = true
      const response = await categoriesApi.createCategory(categoryData)

      if (response?.data?.success) {
        // Refresh categories list
        await fetchCategories()

        ElMessage.success(response?.data?.message || 'Tạo danh mục thành công')
        return response?.data?.data
      } else {
        throw new Error(response?.data?.message || 'Không thể tạo danh mục')
      }
    } catch (error) {
      console.error('Error creating category:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)
      } else {
        const errorMessage = message || 'Không thể tạo danh mục'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const getCategoryById = async (id) => {
    try {
      loading.value = true
      const response = await categoriesApi.getCategoryById(id)

      if (response?.data?.success) {
        currentCategory.value = response?.data?.data

        return response?.data?.data
      } else {
        throw new Error(response?.data?.message || 'Không thể tải thông tin danh mục')
      }
    } catch (error) {
      console.error('Error fetching category:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Không thể tải thông tin danh mục'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateCategory = async (id, categoryData) => {
    try {
      loading.value = true
      const response = await categoriesApi.updateCategory(id, categoryData)

      if (response?.data?.success) {
        // Refresh categories list
        await fetchCategories()

        ElMessage.success(response?.data?.message || 'Cập nhật danh mục thành công')
        return response?.data?.data
      } else {
        throw new Error(response?.data?.message || 'Không thể cập nhật danh mục')
      }
    } catch (error) {
      console.error('Error updating category:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)
      } else {
        const errorMessage = message || 'Không thể cập nhật danh mục'
        ElMessage.error(errorMessage)
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteCategory = async (id) => {
    try {
      await ElMessageBox.confirm(
        'Bạn có chắc chắn muốn xóa danh mục này? Hành động này không thể hoàn tác.',
        'Xác nhận xóa',
        {
          confirmButtonText: 'Xóa',
          cancelButtonText: 'Hủy',
          type: 'warning',
        },
      )

      loading.value = true
      const response = await categoriesApi.deleteCategory(id)

      // Check for 204 No Content (successful deletion)
      if (response?.status === 204 || (response?.data && response?.data?.success)) {
        // Refresh categories list
        await fetchCategories()

        ElMessage.success('Xóa danh mục thành công')
        return true
      } else {
        throw new Error('Không thể xóa danh mục')
      }
    } catch (error) {
      if (error === 'cancel') {
        return // User cancelled
      }
      console.error('Error deleting category:', error)

      // Handle different response statuses
      if (error.response?.status === 204) {
        // 204 is actually success for DELETE
        await fetchCategories()
        ElMessage.success('Xóa danh mục thành công')
        return true
      }

      const errorMessage = error.response?.data?.message || error.message || 'Không thể xóa danh mục'
      ElMessage.error(errorMessage)
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateCategoryStatus = async (id, field, value) => {
    try {

      console.log('Updating category status:', id, field, value)

      const response = await categoriesApi.updateCategoryStatus(id, field, value)
      

      if (response?.data?.success) {
        // Update local state
        updateCategoryInTree(categories.value, id, { [field]: value })

        ElMessage.success(response?.data?.message || 'Cập nhật trạng thái thành công')
        return response?.data?.data
      } else {
        throw new Error(response?.data?.message || 'Không thể cập nhật trạng thái')
      }
    } catch (error) {
      console.error('Error updating category status:', error)

      // Kiểm tra errors trực tiếp từ error object hoặc từ error.response.data
      const errors = error.errors || error.response?.data?.errors
      const message = error.message || error.response?.data?.message

      if (errors) {
        console.error('Validation errors:', errors)
        // Hiển thị lỗi đầu tiên hoặc tất cả lỗi
        const errorMessages = Object.values(errors).flat()
        const errorMessage = errorMessages.length > 0 ? errorMessages.join(', ') : 'Dữ liệu không hợp lệ'
        ElMessage.error(errorMessage)
      } else {
        const errorMessage = message || 'Không thể cập nhật trạng thái'
        ElMessage.error(errorMessage)
      }

      throw error
    }
  }

  // Search and filter
  const searchCategories = async (searchTerm) => {
    // Backend expects search params as field => value pairs
    // Based on the PHP code: foreach ($searchParams as $field => $value)
    if (searchTerm && searchTerm.trim()) {
      searchParams.name = searchTerm.trim() // Search in name field
      delete searchParams.search // Remove generic search if exists
    } else {
      delete searchParams.name
      delete searchParams.search
    }
    searchParams.page = 1
    pagination.value.current_page = 1
    await fetchCategories()
  }

  const filterByStatus = async (status) => {    
    // Fix: Check for null, undefined, and empty string specifically, but allow 0
    if (status !== null && status !== undefined && status !== '') {
      searchParams.status = status
    } else {
      delete searchParams.status
    }
    
    searchParams.page = 1
    pagination.value.current_page = 1
    
    await fetchCategories()
  }

  const resetFilters = async () => {
    // Clear all search params
    delete searchParams.search
    delete searchParams.name
    delete searchParams.status
    searchParams.page = 1
    pagination.value.current_page = 1
    await fetchCategories()
  }

  // Pagination handling
  const handlePageChange = async (page) => {
    searchParams.page = page
    await fetchCategories()
  }

  const handlePerPageChange = async (perPage) => {
    searchParams.limit = perPage
    searchParams.page = 1
    pagination.value.current_page = 1
    await fetchCategories()
  }

  // Utility functions
  const findCategoryInTree = (tree, categoryId) => {
    for (const category of tree) {
      if (category.id === categoryId) {
        return category
      }
      if (category.children && category.children.length > 0) {
        const found = findCategoryInTree(category.children, categoryId)
        if (found) return found
      }
    }
    return null
  }

  const updateCategoryInTree = (tree, categoryId, updates) => {
    for (const category of tree) {
      if (category.id === categoryId) {
        Object.assign(category, updates)
        return true
      }
      if (category.children && category.children.length > 0) {
        if (updateCategoryInTree(category.children, categoryId, updates)) {
          return true
        }
      }
    }
    return false
  }

  const flattenCategoryTree = (tree) => {
    const flattened = []

    const flatten = (categories, level = 0) => {
      categories.forEach((category) => {
        flattened.push({ ...category, level })
        if (category.children && category.children.length > 0) {
          flatten(category.children, level + 1)
        }
      })
    }

    flatten(tree)
    return flattened
  }

  const getCategoryOptions = (tree, excludeId = null) => {
    const options = [{ value: null, label: 'Không có danh mục cha' }]

    const buildOptions = (categories, prefix = '') => {
      categories.forEach((category) => {
        if (category.id !== excludeId) {
          options.push({
            value: category.id,
            label: prefix + category.name,
          })

          if (category.children && category.children.length > 0) {
            buildOptions(category.children, prefix + '── ')
          }
        }
      })
    }

    buildOptions(tree)
    return options
  }

  const getStatusOptions = () => [
    { value: '', label: 'Tất cả trạng thái' },
    { value: 1, label: 'Đã xuất bản' },
    { value: 0, label: 'Nháp' },
  ]

  const getStatusLabel = (status) => {
    return status === 1 ? 'Đã xuất bản' : 'Nháp'
  }

  const getStatusType = (status) => {
    return status === 1 ? 'success' : 'info'
  }

  return {
    // State
    loading,
    categories,
    currentCategory,

    pagination,
    searchParams,

    // CRUD operations
    fetchCategories,
    createCategory,
    getCategoryById,
    updateCategory,
    deleteCategory,
    updateCategoryStatus,

    // Search and filter
    searchCategories,
    filterByStatus,
    resetFilters,

    // Pagination
    handlePageChange,
    handlePerPageChange,

    // Utility functions
    findCategoryInTree,
    updateCategoryInTree,
    flattenCategoryTree,
    getCategoryOptions,
    getStatusOptions,
    getStatusLabel,
    getStatusType,
  }
}
