<template>
  <div class="site-settings-wrapper">
    <PageBreadcrumb :page-title="currentPageTitle" :breadcrumbs="[{ label: 'Quản lý CMS', to: '/cms' }]" />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Two Column Layout: 4:6 ratio -->
      <div class="grid grid-cols-1 gap-6 lg:h-[650px] lg:grid-cols-10 lg:items-stretch">
        <!-- Column 1: Setting Groups List (4/10 width) -->
        <div class="h-full space-y-4 lg:col-span-4">
          <SettingGroupSidebar
            :groups="siteSettingGroups"
            :selected-group="selectedGroup"
            :loading="loading"
            :loading-more="loadingMoreGroups"
            :has-more-pages="groupsPagination.has_more_pages"
            @create-group="handleCreateGroup"
            @edit-group="handleEditGroup"
            @delete-group="handleDeleteGroup"
            @select-group="handleSelectGroup"
            @refresh="handleRefresh"
            @load-more="handleLoadMoreGroups"
          />
        </div>

        <!-- Column 2: Site Settings List (6/10 width) -->
        <div class="settings-panel lg:col-span-6">
          <!-- Panel Header -->
          <div class="panel-header">
            <div class="header-content">
              <h3 class="panel-title">
                {{
                  selectedGroup ? `${selectedGroup.name} - ${totalSettings} cài đặt` : 'Chọn nhóm để quản lý cài đặt'
                }}
              </h3>
              <ButtonCommon v-if="selectedGroup" :icon="PlusIcon" type="primary" size="small" @click="handleAddSetting">
                Thêm cài đặt
              </ButtonCommon>
            </div>
          </div>

          <!-- Panel Content -->
          <div class="panel-content">
            <div v-if="!selectedGroup" class="empty-state">
              <div class="empty-icon">⚙️</div>
              <p class="empty-text">Chọn một nhóm để xem cài đặt</p>
            </div>

            <div v-else-if="siteSettings.length === 0 && !loadingSettings" class="empty-state">
              <div class="empty-icon">📝</div>
              <p class="empty-text">Chưa có cài đặt nào trong nhóm này</p>
              <ButtonCommon type="primary" size="small" :icon="PlusIcon" @click="handleAddSetting">
                Thêm cài đặt
              </ButtonCommon>
            </div>

            <div v-else-if="loadingSettings" class="empty-state">
              <el-icon class="mb-4 animate-spin text-2xl text-gray-400">
                <Loading />
              </el-icon>
              <p class="empty-text">Đang tải cài đặt...</p>
            </div>

            <!-- Settings Table -->
            <div v-else-if="selectedGroup" class="settings-table-container">
              <el-table 
                :data="siteSettings" 
                style="width: 100%" 
                empty-text="Chưa có cài đặt nào" 
                size="small"
                @scroll="handleSettingsScroll"
                ref="settingsTableRef"
                class="settings-table"
              >
                <el-table-column prop="name" label="Tên cài đặt" min-width="120" show-overflow-tooltip class-name="name-column">
                  <template #default="{ row }">
                    <div class="font-medium text-gray-900 dark:text-white">{{ row.name }}</div>
                  </template>
                </el-table-column>

                <el-table-column prop="key" label="Khóa" min-width="130" show-overflow-tooltip class-name="key-column">
                  <template #default="{ row }">
                    <el-tag size="small" type="warning">{{ row.key }}</el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="type" label="Loại" min-width="80" align="center" class-name="type-column">
                  <template #default="{ row }">
                    <el-tag :type="getTypeColor(row.type)" size="small">
                      {{ row.type }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="Trạng thái" min-width="100" align="center" class-name="status-column">
                  <template #default="{ row }">
                    <el-switch
                      :model-value="row.status === 1"
                      @change="handleToggleStatus(row, $event)"
                      size="small"
                      :loading="statusLoadingMap.get(row.id) || false"
                      :disabled="statusLoadingMap.get(row.id) || false"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="Thao tác" min-width="180" align="center" class-name="actions-column" fixed="right">
                  <template #default="{ row }">
                    <ActionButtons
                      :show-view="false"
                      :show-edit="true"
                      :show-delete="true"
                      :delete-disabled="isSettingProtected(row)"
                      :delete-tooltip="getSettingTooltip(row)"
                      @edit="editSetting(row)"
                      @delete="deleteSetting(row.id)"
                    />
                  </template>
                </el-table-column>
              </el-table>

              <!-- Loading more settings indicator -->
              <div v-if="loadingMoreSettings" class="py-4 text-center border-t">
                <el-icon class="animate-spin text-blue-500">
                  <Loading />
                </el-icon>
                <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">Đang tải thêm cài đặt...</div>
              </div>

              <!-- Load more button when table scroll doesn't work well -->
              <div v-if="!loadingMoreSettings && settingsPagination.has_more_pages" class="py-4 text-center border-t">
                <ButtonCommon 
                  type="primary" 
                  size="small" 
                  @click="handleLoadMoreSettings"
                  :loading="loadingMoreSettings"
                >
                  Tải thêm cài đặt
                </ButtonCommon>
              </div>
            </div>

            <!-- Total count indicator -->
            <div v-if="siteSettings.length > 0" class="items-count">Tổng số: {{ totalSettings }} cài đặt</div>
          </div>
        </div>
      </div>

      <!-- Group Form Modal -->
      <el-dialog
        v-model="showCreateGroupDialog"
        :title="editingGroup ? 'Chỉnh sửa nhóm cài đặt' : 'Tạo nhóm cài đặt mới'"
        width="600px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="groupFormRef"
          :model="groupFormData"
          :rules="groupFormRules"
          label-width="120px"
          label-position="top"
          :hide-required-asterisk="true"
        >
          <el-form-item prop="name">
            <template #label>
              <span class="flex items-center">
                Tên nhóm
                <span class="ml-1 text-red-500">*</span>
              </span>
            </template>
            <el-input
              v-model="groupFormData.name"
              placeholder="Nhập tên nhóm cài đặt"
              maxlength="255"
              @input="(val) => { generateGroupSlug(val); groupNameHandlers.onInput(val); }"
              @blur="groupNameHandlers.onBlur"
            />
          </el-form-item>

          <el-form-item prop="group_key">
            <template #label>
              <span class="flex items-center">
                Khóa nhóm
                <span class="ml-1 text-red-500">*</span>
              </span>
            </template>
            <el-input
              v-model="groupFormData.group_key"
              placeholder="Nhập khóa nhóm (ví dụ: general-info)"
              maxlength="255"
              @input="groupKeyHandlers.onInput"
              @blur="groupKeyHandlers.onBlur"
            />
            <div class="mt-1 text-sm text-gray-500">
              Khóa nhóm phải là duy nhất và chỉ chứa chữ cái, số và dấu gạch ngang
            </div>
          </el-form-item>

          <el-form-item label="Mô tả" prop="description">
            <el-input
              v-model="groupFormData.description"
              type="textarea"
              :rows="3"
              placeholder="Nhập mô tả cho nhóm cài đặt"
              maxlength="500"
              @input="groupDescriptionHandlers.onInput"
              @blur="groupDescriptionHandlers.onBlur"
            />
          </el-form-item>

          <el-form-item label="Thứ tự" prop="order">
            <el-input-number
              v-model="groupFormData.order"
              :min="0"
              :max="999"
              placeholder="Thứ tự hiển thị"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>

        <ButtonModalCommon 
        :loading="loading"
        :can-submit="true"
        cancel-text="Hủy"
        :submit-text="editingGroup ? 'Cập nhật' : 'Thêm mới'"
        @cancel="() => { showCreateGroupDialog = false; resetGroupForm(); }"
        @submit="handleSubmitGroup"
      />
      </el-dialog>

      <!-- Setting Form Modal -->
      <el-dialog
        v-model="showCreateSettingDialog"
        :title="editingSetting ? 'Chỉnh sửa cài đặt' : 'Tạo cài đặt mới'"
        width="600px"
        :close-on-click-modal="false"
      >
        <el-form
          ref="settingFormRef"
          :model="settingFormData"
          :rules="settingFormRules"
          label-width="120px"
          label-position="top"
          :hide-required-asterisk="true"
        >
          <el-form-item prop="name">
            <template #label>
              <span class="flex items-center">
                Tên cài đặt
                <span class="ml-1 text-red-500">*</span>
              </span>
            </template>
            <el-input
              v-model="settingFormData.name"
              placeholder="Nhập tên cài đặt"
              maxlength="255"
              @input="(val) => { generateSettingSlug(val); settingNameHandlers.onInput(val); }"
              @blur="settingNameHandlers.onBlur"
            />
          </el-form-item>

          <el-form-item prop="key">
            <template #label>
              <span class="flex items-center">
                Khóa cài đặt
                <span class="ml-1 text-red-500">*</span>
              </span>
            </template>
            <el-input
              v-model="settingFormData.key"
              placeholder="Nhập khóa cài đặt (ví dụ: site-name)"
              maxlength="255"
              @input="settingKeyHandlers.onInput"
              @blur="settingKeyHandlers.onBlur"
            />
            <div class="mt-1 text-sm text-gray-500">
              Khóa cài đặt phải là duy nhất và chỉ chứa chữ cái, số và dấu gạch ngang
            </div>
          </el-form-item>

          <el-form-item prop="type">
            <template #label>
              <span class="flex items-center">
                Loại cài đặt
                <span class="ml-1 text-red-500">*</span>
              </span>
            </template>
            <el-select v-model="settingFormData.type" placeholder="Chọn loại cài đặt" style="width: 100%">
              <el-option v-for="type in settingTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
          </el-form-item>

          <el-form-item prop="value">
            <template #label>
              <span class="flex items-center">
                Giá trị
                <span class="ml-1 text-red-500">*</span>
              </span>
            </template>
            <!-- Text Input -->
            <el-input
              v-if="settingFormData.type === 'text'"
              v-model="settingFormData.value"
              placeholder="Nhập văn bản..."
              maxlength="1000"
              @input="settingValueHandlers.onInput"
              @blur="settingValueHandlers.onBlur"
            />

            <!-- Textarea -->
            <el-input
              v-else-if="settingFormData.type === 'textarea'"
              v-model="settingFormData.value"
              type="textarea"
              :rows="4"
              placeholder="Nhập nội dung..."
              maxlength="5000"
              @input="settingValueHandlers.onInput"
              @blur="settingValueHandlers.onBlur"
            />

            <!-- Editor (Rich Text) -->
            <el-input
              v-else-if="settingFormData.type === 'editor'"
              v-model="settingFormData.value"
              type="textarea"
              :rows="6"
              placeholder="Nhập nội dung HTML hoặc rich text..."
              maxlength="10000"
              @input="settingValueHandlers.onInput"
              @blur="settingValueHandlers.onBlur"
            />

            <!-- Switch (Boolean) -->
            <div v-else-if="settingFormData.type === 'switch'" class="flex items-center gap-2">
              <el-switch v-model="booleanValue" @change="settingFormData.value = $event ? '1' : '0'" />
              <span class="text-sm text-gray-600">{{ booleanValue ? 'Bật' : 'Tắt' }}</span>
            </div>

            <!-- Image Upload -->
            <div v-else-if="settingFormData.type === 'image'">
              <el-upload
                :before-upload="handleBeforeUpload"
                :http-request="handleImageUpload"
                :show-file-list="false"
                accept="image/*"
                drag
                class="w-full"
              >
                <div v-if="!settingFormData.value && !settingFormData.preview" class="py-8 text-center">
                  <Plus class="mx-auto mb-2 h-8 w-8 text-gray-400" />
                  <div class="text-gray-600 dark:text-gray-300">Kéo thả ảnh vào đây hoặc <em>click để chọn</em></div>
                  <div class="mt-1 text-xs text-gray-400">Hỗ trợ: JPG, PNG, GIF (tối đa 5MB)</div>
                  <div v-if="editingSetting && editingSetting.value" class="mt-2 text-xs text-blue-500">
                    Để trống để giữ ảnh hiện tại
                  </div>
                </div>
                <div v-else class="group relative">
                  <img
                    :src="settingFormData.preview || settingFormData.value"
                    alt="Setting Image"
                    class="mx-auto max-h-32 max-w-full rounded object-cover"
                  />
                  <div
                    class="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded bg-black opacity-0 transition-opacity group-hover:opacity-100"
                  >
                    <div class="flex gap-2">
                      <ButtonCommon type="primary" size="small">Thay đổi</ButtonCommon>
                      <ButtonCommon v-if="settingFormData.value" type="danger" size="small" @click.stop="clearImage">
                        Xóa
                      </ButtonCommon>
                    </div>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- Document Upload -->
            <div v-else-if="settingFormData.type === 'document'">
              <el-upload
                :before-upload="handleBeforeDocumentUpload"
                :http-request="handleDocumentUpload"
                :show-file-list="false"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                drag
                class="w-full"
              >
                <div v-if="!settingFormData.value && !settingFormData.preview" class="py-8 text-center">
                  <Plus class="mx-auto mb-2 h-8 w-8 text-gray-400" />
                  <div class="text-gray-600 dark:text-gray-300">
                    Kéo thả tài liệu vào đây hoặc <em>click để chọn</em>
                  </div>
                  <div class="mt-1 text-xs text-gray-400">
                    Hỗ trợ: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT (tối đa 10MB)
                  </div>
                  <div v-if="editingSetting && editingSetting.value" class="mt-2 text-xs text-blue-500">
                    Để trống để giữ tài liệu hiện tại
                  </div>
                </div>
                <div v-else class="group relative">
                  <div class="flex items-center justify-center rounded bg-gray-100 p-4 dark:bg-gray-700">
                    <div class="text-center">
                      <div class="text-lg font-medium text-gray-700 dark:text-gray-300">
                        {{ getFileName(settingFormData.value) }}
                      </div>
                      <div class="text-sm text-gray-500">Tài liệu đã tải lên</div>
                    </div>
                  </div>
                  <div
                    class="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded bg-black opacity-0 transition-opacity group-hover:opacity-100"
                  >
                    <div class="flex gap-2">
                      <ButtonCommon type="primary" size="small">Thay đổi</ButtonCommon>
                      <ButtonCommon v-if="settingFormData.value" type="danger" size="small" @click.stop="clearImage">
                        Xóa
                      </ButtonCommon>
                    </div>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- Video Upload -->
            <div v-else-if="settingFormData.type === 'video'">
              <el-upload
                :before-upload="handleBeforeVideoUpload"
                :http-request="handleVideoUpload"
                :show-file-list="false"
                accept="video/*"
                drag
                class="w-full"
              >
                <div v-if="!settingFormData.value && !settingFormData.preview" class="py-8 text-center">
                  <Plus class="mx-auto mb-2 h-8 w-8 text-gray-400" />
                  <div class="text-gray-600 dark:text-gray-300">Kéo thả video vào đây hoặc <em>click để chọn</em></div>
                  <div class="mt-1 text-xs text-gray-400">Hỗ trợ: MP4, AVI, MOV, WMV (tối đa 50MB)</div>
                  <div v-if="editingSetting && editingSetting.value" class="mt-2 text-xs text-blue-500">
                    Để trống để giữ video hiện tại
                  </div>
                </div>
                <div v-else class="group relative">
                  <div class="flex items-center justify-center rounded bg-gray-100 p-4 dark:bg-gray-700">
                    <div class="text-center">
                      <div class="text-lg font-medium text-gray-700 dark:text-gray-300">
                        {{ getFileName(settingFormData.value) }}
                      </div>
                      <div class="text-sm text-gray-500">Video đã tải lên</div>
                    </div>
                  </div>
                  <div
                    class="bg-opacity-50 absolute inset-0 flex items-center justify-center rounded bg-black opacity-0 transition-opacity group-hover:opacity-100"
                  >
                    <div class="flex gap-2">
                      <ButtonCommon type="primary" size="small">Thay đổi</ButtonCommon>
                      <ButtonCommon v-if="settingFormData.value" type="danger" size="small" @click.stop="clearImage">
                        Xóa
                      </ButtonCommon>
                    </div>
                  </div>
                </div>
              </el-upload>
            </div>

            <!-- Default Text Input -->
            <el-input 
              v-else 
              v-model="settingFormData.value" 
              placeholder="Nhập giá trị" 
              maxlength="1000" 
              @input="settingValueHandlers.onInput"
              @blur="settingValueHandlers.onBlur"
            />
          </el-form-item>

          <el-form-item label="Trạng thái" prop="status">
            <el-switch
              v-model="statusValue"
              @change="settingFormData.status = $event ? 1 : 0"
              active-text="Hoạt động"
              inactive-text="Tắt"
            />
          </el-form-item>
        </el-form>

        <ButtonModalCommon 
        :loading="loading"
        :can-submit="true"
        cancel-text="Hủy"
        :submit-text="editingSetting ? 'Cập nhật' : 'Thêm mới'"
        @cancel="() => { showCreateSettingDialog = false; resetSettingForm(); }"
        @submit="handleSubmitSetting"
      />
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Loading, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSiteSettings } from '@/composables/modules/cms/useSiteSettings.js'
import { useFormValidation, validationPresets } from '@/composables/useFormValidation.js'
import { generateSlugWithDash } from '@/utils/helpers/string.helper.js'
import {
  isProtectedSetting,
  getProtectedSettingTooltipMessage
} from '@/utils/configs/protected-settings.config.js'
import { PlusIcon } from '@/components/icons/index.js'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import SettingGroupSidebar from '@/components/modules/cms/settings/SettingGroupSidebar.vue'
import ButtonModalCommon from '@/components/common/ButtonModalCommon.vue'

// Router
const router = useRouter()

// Composables
const {
  loading,
  loadingSettings,
  loadingMoreGroups,
  loadingMoreSettings,
  siteSettingGroups,
  siteSettings,
  groupsPagination,
  settingsPagination,
  fetchSiteSettingGroups,
  loadMoreGroups,
  fetchSiteSettings,
  loadMoreSettings,
  createSiteSettingGroup,
  updateSiteSettingGroup,
  deleteSiteSettingGroup,
  createSiteSetting,
  updateSiteSetting,
  deleteSiteSetting,
  patchSiteSettingAttributes,
} = useSiteSettings()

// Form validation composable
const { 
  formRef: groupFormRef, 
  validateForm: validateGroupForm,
  clearAllValidation: clearGroupValidation,
  clearFieldValidation,
  createFieldHandlers: createGroupFieldHandlers
} = useFormValidation()

const { 
  formRef: settingFormRef, 
  validateForm: validateSettingForm,
  clearAllValidation: clearSettingValidation,
  createFieldHandlers: createSettingFieldHandlers,
  autoGenerateField
} = useFormValidation()

// Page data
const currentPageTitle = 'Cài đặt hệ thống'

// Selected states
const selectedGroup = ref(null)
const editingGroup = ref(null)
const editingSetting = ref(null)
const totalSettings = computed(() => siteSettings.value?.length || 0)

// Loading states for individual settings
const statusLoadingMap = ref(new Map())

// Modal states
const showCreateGroupDialog = ref(false)
const showCreateSettingDialog = ref(false)
const settingsTableRef = ref()

// Form data for groups
const groupFormData = reactive({
  name: '',
  group_key: '',
  description: '',
  order: 0,
})

// Create input handlers for group form real-time validation
const groupNameHandlers = createGroupFieldHandlers('name', validationPresets.name)
const groupKeyHandlers = createGroupFieldHandlers('group_key', validationPresets.slug)
const groupDescriptionHandlers = createGroupFieldHandlers('description', validationPresets.description)

// Form data for settings
const settingFormData = reactive({
  name: '',
  key: '',
  type: 'text',
  value: '',
  status: 1,
  preview: '', // For image preview
  fileObject: null, // Store the actual file object for uploads
})

// Create input handlers for setting form real-time validation
const settingNameHandlers = createSettingFieldHandlers('name', validationPresets.name)
const settingKeyHandlers = createSettingFieldHandlers('key', validationPresets.slug)
const settingValueHandlers = createSettingFieldHandlers('value', { required: true, minLength: 0 })

// Form rules for groups
const groupFormRules = {
  name: [
    { required: true, message: 'Vui lòng nhập tên nhóm', trigger: 'blur' },
    { min: 2, max: 255, message: 'Tên nhóm phải từ 2-255 ký tự', trigger: 'blur' },
  ],
  group_key: [
    { required: true, message: 'Vui lòng nhập khóa nhóm', trigger: 'blur' },
    { min: 2, max: 255, message: 'Khóa nhóm phải từ 2-255 ký tự', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9-]+$/, message: 'Khóa nhóm chỉ được chứa chữ cái, số và dấu gạch ngang', trigger: 'blur' },
  ],
  description: [{ max: 500, message: 'Mô tả không được vượt quá 500 ký tự', trigger: 'blur' }],
  order: [{ type: 'number', min: 0, max: 999, message: 'Thứ tự phải từ 0-999', trigger: 'blur' }],
}

// Form rules for settings
const settingFormRules = {
  name: [
    { required: true, message: 'Vui lòng nhập tên cài đặt', trigger: 'blur' },
    { min: 2, max: 255, message: 'Tên cài đặt phải từ 2-255 ký tự', trigger: 'blur' },
  ],
  key: [
    { required: true, message: 'Vui lòng nhập khóa cài đặt', trigger: 'blur' },
    { min: 2, max: 255, message: 'Khóa cài đặt phải từ 2-255 ký tự', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9-]+$/, message: 'Khóa cài đặt chỉ được chứa chữ cái, số và dấu gạch ngang', trigger: 'blur' },
  ],
  type: [{ required: true, message: 'Vui lòng chọn loại cài đặt', trigger: 'change' }],
  value: [{ required: true, message: 'Vui lòng nhập giá trị', trigger: 'blur' }],
}

// Setting types
const settingTypes = [
  { value: 'text', label: 'Văn bản' },
  { value: 'textarea', label: 'Văn bản dài' },
  { value: 'editor', label: 'Soạn thảo' },
  { value: 'switch', label: 'Bật/Tắt' },
  // { value: 'number', label: 'Số' },
  // { value: 'email', label: 'Email' },
  // { value: 'url', label: 'URL' },
  { value: 'image', label: 'Hình ảnh' },
  { value: 'document', label: 'Tài liệu' },
  { value: 'video', label: 'Video' },
]

// Boolean value for switch type
const booleanValue = computed({
  get: () => settingFormData.value === '1' || settingFormData.value === true,
  set: (val) => {
    settingFormData.value = val ? '1' : '0'
  },
})

// Status value for switch
const statusValue = computed({
  get: () => settingFormData.status === 1,
  set: (val) => {
    settingFormData.status = val ? 1 : 0
  },
})

// Methods for Group Management
const handleCreateGroup = () => {
  editingGroup.value = null
  resetGroupForm()
  showCreateGroupDialog.value = true
}

const handleEditGroup = (group) => {
  editingGroup.value = group
  Object.assign(groupFormData, {
    name: group.name,
    group_key: group.group_key,
    description: group.description || '',
    order: group.order || 0,
  })
  showCreateGroupDialog.value = true
}

const handleDeleteGroup = async (groupId) => {
  await deleteSiteSettingGroup(groupId)
  await fetchSiteSettingGroups()

  // Clear selected group if it was deleted
  if (selectedGroup.value && selectedGroup.value.id === groupId) {
    selectedGroup.value = null
  }
}

const handleSelectGroup = async (group) => {
  selectedGroup.value = group
  await loadSettingsData()
}

const handleRefresh = async () => {
  await fetchSiteSettingGroups()
  if (selectedGroup.value) {
    await loadSettingsData()
  }
}

const handleLoadMoreGroups = async () => {
  await loadMoreGroups()
}

const loadSettingsData = async () => {
  if (selectedGroup.value?.id) {
    try {
      await fetchSiteSettings(selectedGroup.value.id)
    } catch (error) {
      console.error('Error loading settings data:', error)
    }
  }
}

// Methods for Setting Management
const handleAddSetting = () => {
  editingSetting.value = null
  resetSettingForm()
  showCreateSettingDialog.value = true
}

const editSetting = (setting) => {
  editingSetting.value = setting
  Object.assign(settingFormData, {
    name: setting.name,
    key: setting.key,
    type: setting.type,
    value: setting.value,
    status: setting.status,
    preview: '', // Reset preview for new file uploads
    fileObject: null, // Reset file object for new file uploads
  })
  showCreateSettingDialog.value = true
}

const deleteSetting = async (settingId) => {
  try {
    // Find the setting to ensure it exists and has the correct group_id
    const setting = siteSettings.value.find((s) => s.id === settingId)
    if (!setting) {
      ElMessage.error('Không tìm thấy cài đặt')
      return
    }

    // Ensure the setting has the correct group_id
    if (!setting.site_setting_group_id && selectedGroup.value) {
      setting.site_setting_group_id = selectedGroup.value.id
    }

    await deleteSiteSetting(settingId)
    // Don't need to reload data as the composable already updates the list
  } catch (error) {
    console.error('Error in deleteSetting:', error)
    if (error !== 'cancel' && error.message !== 'cancel') {
      ElMessage.error('Có lỗi xảy ra khi xóa cài đặt')
    }
  }
}

// Protected setting methods
const isSettingProtected = (setting) => {
  return isProtectedSetting(setting.key, setting.name)
}

const getSettingTooltip = (setting) => {
  return getProtectedSettingTooltipMessage(setting.key)
}

const handleToggleStatus = async (setting, newStatus) => {
  // Prevent multiple simultaneous status changes
  if (statusLoadingMap.value.get(setting.id)) {
    return
  }

  try {
    // Set loading state in the Map
    statusLoadingMap.value.set(setting.id, true)

    // Use PATCH to update only the status attribute
    const result = await patchSiteSettingAttributes(selectedGroup.value.id, setting.id, {
      status: newStatus ? 1 : 0
    })

    // Update local status (composable should handle this automatically)
    setting.status = newStatus ? 1 : 0

  } catch (error) {
    console.error('Error toggling setting status:', error)
    
    // Don't show error message again if composable already showed it
    if (!error.message?.includes('Có lỗi xảy ra khi cập nhật thuộc tính')) {
      ElMessage.error('Có lỗi xảy ra khi cập nhật trạng thái')
    }

    // Revert the switch state on error
    setting.status = setting.status === 1 ? 0 : 1
  } finally {
    // Always remove loading state
    statusLoadingMap.value.set(setting.id, false)
    
    // Clean up the Map entry after a short delay
    setTimeout(() => {
      statusLoadingMap.value.delete(setting.id)
    }, 100)
  }
}

const handleLoadMoreSettings = async () => {
  if (selectedGroup.value?.id) {
    await loadMoreSettings(selectedGroup.value.id)
  }
}

const handleSettingsScroll = (event) => {
  if (loadingMoreSettings.value || !settingsPagination.has_more_pages) return

  const { scrollTop, scrollHeight, clientHeight } = event.target
  const threshold = 50 // pixels from bottom

  if (scrollHeight - scrollTop - clientHeight < threshold) {
    handleLoadMoreSettings()
  }
}

// Form handlers
const handleSubmitGroup = async () => {
  try {
    const isValid = await validateGroupForm()
    if (!isValid) return

    if (editingGroup.value) {
      await updateSiteSettingGroup(editingGroup.value.id, groupFormData)
    } else {
      await createSiteSettingGroup(groupFormData)
    }

    showCreateGroupDialog.value = false
    resetGroupForm()
    await fetchSiteSettingGroups()
  } catch (error) {
    console.error('Error submitting group form:', error)
  }
}

const handleSubmitSetting = async () => {
  try {
    const isValid = await validateSettingForm()
    if (!isValid) return

    // Create FormData for file uploads
    const formData = new FormData()

    // Add basic setting data
    formData.append('name', settingFormData.name)
    formData.append('key', settingFormData.key)
    formData.append('type', settingFormData.type)
    formData.append('status', settingFormData.status)
    formData.append('site_setting_group_id', selectedGroup.value.id) // For composable extraction
    formData.append('group_id', selectedGroup.value.id) // For API compatibility

    // Handle different value types
    if (settingFormData.type === 'image' || settingFormData.type === 'document' || settingFormData.type === 'video') {
      // For file uploads, the file will be handled by the upload handlers
      if (settingFormData.fileObject) {
        formData.append('value', settingFormData.fileObject)
      } else if (settingFormData.value) {
        formData.append('value', settingFormData.value)
      }
    } else {
      formData.append('value', settingFormData.value || '')
    }

    if (editingSetting.value) {
      await updateSiteSetting(editingSetting.value.id, formData)
    } else {
      await createSiteSetting(formData)
    }

    showCreateSettingDialog.value = false
    resetSettingForm()
    await loadSettingsData()
  } catch (error) {
    console.error('Error submitting setting form:', error)
  }
}

// Helper methods
const resetGroupForm = () => {
  Object.assign(groupFormData, {
    name: '',
    group_key: '',
    description: '',
    order: 0,
  })
  editingGroup.value = null
  clearGroupValidation()
}

const resetSettingForm = () => {
  Object.assign(settingFormData, {
    name: '',
    key: '',
    type: 'text',
    value: '',
    status: 1,
    preview: '',
    fileObject: null,
  })
  editingSetting.value = null
  clearSettingValidation()
}

const getTypeColor = (type) => {
  const colors = {
    text: 'primary',
    textarea: 'success',
    switch: 'warning',
    number: 'info',
    email: 'danger',
    url: 'primary',
  }
  return colors[type] || 'primary'
}

const getTypeLabel = (type) => {
  const typeObj = settingTypes.find((t) => t.value === type)
  return typeObj ? typeObj.label : type
}

// File upload handlers
const handleBeforeUpload = (file) => {
  const isValidSize = file.size / 1024 / 1024 < 5 // 5MB
  if (!isValidSize) {
    ElMessage.error('Kích thước file không được vượt quá 5MB!')
    return false
  }
  return true
}

const handleBeforeDocumentUpload = (file) => {
  const isValidSize = file.size / 1024 / 1024 < 10 // 10MB
  if (!isValidSize) {
    ElMessage.error('Kích thước file không được vượt quá 10MB!')
    return false
  }
  return true
}

const handleBeforeVideoUpload = (file) => {
  const isValidSize = file.size / 1024 / 1024 < 50 // 50MB
  if (!isValidSize) {
    ElMessage.error('Kích thước file không được vượt quá 50MB!')
    return false
  }
  return true
}

const handleImageUpload = async (options) => {
  try {
    // Store the file object for form submission
    settingFormData.fileObject = options.file

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      settingFormData.preview = e.target.result
    }
    reader.readAsDataURL(options.file)

    // Set display name
    settingFormData.value = options.file.name

    ElMessage.success('Tải ảnh thành công!')
  } catch (error) {
    console.error('Error uploading image:', error)
    ElMessage.error('Lỗi khi tải ảnh lên')
  }
}

const handleDocumentUpload = async (options) => {
  try {
    // Store the file object for form submission
    settingFormData.fileObject = options.file

    // Set display name
    settingFormData.value = options.file.name

    ElMessage.success('Tải tài liệu thành công!')
  } catch (error) {
    console.error('Error uploading document:', error)
    ElMessage.error('Lỗi khi tải tài liệu lên')
  }
}

const handleVideoUpload = async (options) => {
  try {
    // Store the file object for form submission
    settingFormData.fileObject = options.file

    // Set display name
    settingFormData.value = options.file.name

    ElMessage.success('Tải video thành công!')
  } catch (error) {
    console.error('Error uploading video:', error)
    ElMessage.error('Lỗi khi tải video lên')
  }
}

const clearImage = () => {
  settingFormData.value = ''
  settingFormData.preview = ''
  settingFormData.fileObject = null
}

const getFileName = (path) => {
  if (!path) return ''
  return path.split('/').pop() || path
}

// Watch for selected group changes
watch(selectedGroup, async (newGroup) => {
  if (newGroup) {
    await loadSettingsData()
  }
})

// Auto-generate slug functions
const generateGroupSlug = (name) => {
  if (!name || editingGroup.value) return // Don't auto-generate when editing
  const newSlug = generateSlugWithDash(name)
  groupFormData.group_key = newSlug
  
  // Clear validation for the group_key field after auto-generation
  nextTick(() => {
    setTimeout(() => {
      if (groupFormRef.value) {
        groupFormRef.value.clearValidate('group_key')
      }
    }, 100)
  })
}

const generateSettingSlug = (name) => {
  if (!name || editingSetting.value) return // Don't auto-generate when editing
  const newSlug = generateSlugWithDash(name)
  settingFormData.key = newSlug
  
  // Clear validation for the key field after auto-generation
  nextTick(() => {
    setTimeout(() => {
      if (settingFormRef.value) {
        settingFormRef.value.clearValidate('key')
      }
    }, 100)
  })
}

// Initialize
onMounted(async () => {
  await fetchSiteSettingGroups()

  // Auto-select first group if available
  if (siteSettingGroups.value && siteSettingGroups.value.length > 0) {
    selectedGroup.value = siteSettingGroups.value[0]
  }
})
</script>

<style scoped>
/* Settings Panel */
.settings-panel {
  display: flex;
  flex-direction: column;
  height: 639px;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #ffffff;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  min-height: 200px;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.settings-table-container {
  height: 100%;
  overflow: auto;
}

/* Increase table text size and row height */
.settings-table-container :deep(.el-table) {
  font-size: 0.9375rem;
}

.settings-table-container :deep(.el-table th),
.settings-table-container :deep(.el-table td) {
  font-size: 0.9375rem;
  padding: 12px 8px !important; /* Increased from default 7px to 12px for more spacing */
}

.settings-table-container :deep(.el-tag) {
  font-size: 0.8125rem;
}

/* Responsive column sizing for settings table */
:deep(.settings-table .name-column) {
  min-width: 120px;
  width: auto;
}

:deep(.settings-table .key-column) {
  min-width: 130px;
  width: auto;
}

:deep(.settings-table .type-column) {
  min-width: 80px;
  width: auto;
}

:deep(.settings-table .status-column) {
  min-width: 100px;
  width: auto;
}

:deep(.settings-table .actions-column) {
  min-width: 180px;
  width: auto;
}

/* Responsive adjustments for settings table */
@media (max-width: 1280px) {
  :deep(.settings-table .name-column) {
    min-width: 100px;
  }
  :deep(.settings-table .key-column) {
    min-width: 110px;
  }
  :deep(.settings-table .type-column) {
    min-width: 70px;
  }
  :deep(.settings-table .status-column) {
    min-width: 90px;
  }
  :deep(.settings-table .actions-column) {
    min-width: 160px;
  }
}

@media (max-width: 1024px) {
  :deep(.settings-table .name-column) {
    min-width: 90px;
  }
  :deep(.settings-table .key-column) {
    min-width: 100px;
  }
  :deep(.settings-table .type-column) {
    min-width: 60px;
  }
  :deep(.settings-table .status-column) {
    min-width: 80px;
  }
  :deep(.settings-table .actions-column) {
    min-width: 140px;
  }
}

@media (max-width: 768px) {
  :deep(.settings-table .name-column) {
    min-width: 80px;
  }
  :deep(.settings-table .key-column) {
    min-width: 90px;
  }
  :deep(.settings-table .type-column) {
    min-width: 50px;
  }
  :deep(.settings-table .status-column) {
    min-width: 70px;
  }
  :deep(.settings-table .actions-column) {
    min-width: 120px;
  }
}

/* Form text sizes */
:deep(.el-form-item__label) {
  font-size: 0.9375rem !important;
}

:deep(.el-input__inner) {
  font-size: 0.9375rem !important;
}

:deep(.el-textarea__inner) {
  font-size: 0.9375rem !important;
}

:deep(.el-select) {
  font-size: 0.9375rem !important;
}

:deep(.el-button) {
  font-size: 0.9375rem !important;
}

.items-count {
  padding: 0.5rem 1rem;
  text-align: center;
  font-size: 0.875rem;
  color: #9ca3af;
  border-top: 1px solid #f3f4f6;
  margin-top: 1rem;
  background-color: #f9fafb;
}

/* Footer Button Styles */
.footer-btn {
  min-width: 80px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
  color: #666 !important;
}

.cancel-btn:hover {
  background: #e6e6e6 !important;
  border-color: #bfbfbf !important;
  color: #333 !important;
}

.submit-btn {
  background: #1890ff !important;
  border: 1px solid #1890ff !important;
  color: #fff !important;
}

.submit-btn:hover {
  background: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* Custom scrollbar */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode support */
.dark .settings-panel {
  border-color: #374151;
  background-color: rgba(255, 255, 255, 0.03);
}

.dark .panel-header {
  border-bottom-color: #374151;
  background-color: rgba(17, 24, 39, 0.5);
}

.dark .panel-title {
  color: #ffffff;
}

.dark .empty-text {
  color: #9ca3af;
}

.dark .items-count {
  color: #6b7280;
  border-top-color: #374151;
}

/* Dark mode scrollbar */
.dark .panel-content::-webkit-scrollbar-track {
  background: #374151;
}

.dark .panel-content::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .panel-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .site-settings-wrapper .grid {
    height: auto !important;
  }

  .settings-panel {
    height: auto !important;
    max-height: 500px;
  }
}
</style>
